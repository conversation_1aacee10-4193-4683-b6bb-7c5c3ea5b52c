# SOW Generator Architecture

## Components
1. **Frontend**: Static website hosted on S3
2. **Backend API**: AWS Lambda + API Gateway
3. **Document Generation**: Lambda function using Python with docx library
4. **Storage**: S3 bucket for template storage and generated documents
5. **Authentication**: Amazon Cognito (free tier eligible)

## Data Flow
1. User authenticates via Cognito
2. User interacts with the frontend to provide SOW details
3. API Gateway routes requests to Lambda functions
4. Lambda processes the input and generates the SOW document
5. Generated document is stored in S3 and provided to the user for download