<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOW Generator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Statement of Work Generator</h1>
        <div id="auth-section">
            <button id="login-button">Login</button>
            <div id="user-info" style="display: none;">
                <p>Logged in as: <span id="username"></span></p>
                <button id="logout-button">Logout</button>
            </div>
        </div>
        
        <div id="sow-form" style="display: none;">
            <h2>SOW Details</h2>
            
            <div class="form-section">
                <h3>1. Description and Purpose</h3>
                <textarea id="description" rows="5" placeholder="Enter project description and purpose..."></textarea>
            </div>
            
            <div class="form-section">
                <h3>2. Project Phases</h3>
                <div id="phases-container">
                    <div class="phase-item">
                        <input type="text" placeholder="Phase name" class="phase-name">
                        <textarea placeholder="Phase description" class="phase-desc"></textarea>
                        <button class="remove-phase">Remove</button>
                    </div>
                </div>
                <button id="add-phase">Add Phase</button>
            </div>
            
            <div class="form-section">
                <h3>3. Timeline and Milestones</h3>
                <div id="milestones-container">
                    <div class="milestone-item">
                        <input type="text" placeholder="Milestone name" class="milestone-name">
                        <input type="date" class="milestone-date">
                        <button class="remove-milestone">Remove</button>
                    </div>
                </div>
                <button id="add-milestone">Add Milestone</button>
            </div>
            
            <div class="form-section">
                <h3>4. Resource Estimates</h3>
                <div id="resources-container">
                    <div class="resource-item">
                        <input type="text" placeholder="Resource role" class="resource-role">
                        <input type="number" placeholder="Quantity" class="resource-qty" min="1">
                        <input type="text" placeholder="Engagement period" class="resource-period">
                        <button class="remove-resource">Remove</button>
                    </div>
                </div>
                <button id="add-resource">Add Resource</button>
            </div>
            
            <button id="generate-sow">Generate SOW</button>
        </div>
        
        <div id="result-section" style="display: none;">
            <h2>Your SOW is Ready!</h2>
            <p>Click the button below to download your Statement of Work document.</p>
            <a id="download-link" class="button">Download SOW</a>
            <button id="create-new">Create Another SOW</button>
        </div>
    </div>
    
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.1001.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/amazon-cognito-identity-js@5.2.10/dist/amazon-cognito-identity.min.js"></script>
    <script src="app.js"></script>
</body>
</html>