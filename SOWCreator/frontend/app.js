// AWS Configuration
const awsConfig = {
    region: 'us-east-1',
    userPoolId: 'YOUR_COGNITO_USER_POOL_ID',
    userPoolWebClientId: 'YOUR_COGNITO_CLIENT_ID',
    apiEndpoint: 'YOUR_API_GATEWAY_ENDPOINT'
};

// Cognito setup
const userPool = new AmazonCognitoIdentity.CognitoUserPool({
    UserPoolId: awsConfig.userPoolId,
    ClientId: awsConfig.userPoolWebClientId
});

// DOM elements
const loginButton = document.getElementById('login-button');
const logoutButton = document.getElementById('logout-button');
const userInfo = document.getElementById('user-info');
const username = document.getElementById('username');
const sowForm = document.getElementById('sow-form');
const resultSection = document.getElementById('result-section');
const downloadLink = document.getElementById('download-link');
const createNewButton = document.getElementById('create-new');

// Form interaction buttons
const addPhaseButton = document.getElementById('add-phase');
const addMilestoneButton = document.getElementById('add-milestone');
const addResourceButton = document.getElementById('add-resource');
const generateSowButton = document.getElementById('generate-sow');

// Check if user is already logged in
function checkAuth() {
    const cognitoUser = userPool.getCurrentUser();
    if (cognitoUser != null) {
        cognitoUser.getSession((err, session) => {
            if (err) {
                console.error(err);
                return;
            }
            
            if (session.isValid()) {
                loginButton.style.display = 'none';
                userInfo.style.display = 'block';
                sowForm.style.display = 'block';
                
                cognitoUser.getUserAttributes((err, attributes) => {
                    if (err) {
                        console.error(err);
                        return;
                    }
                    
                    const emailAttribute = attributes.find(attr => attr.Name === 'email');
                    username.textContent = emailAttribute ? emailAttribute.Value : 'User';
                });
            }
        });
    }
}

// Event listeners
loginButton.addEventListener('click', () => {
    // In a real app, redirect to a login page or show a modal
    // For simplicity, we'll just simulate a successful login
    loginButton.style.display = 'none';
    userInfo.style.display = 'block';
    sowForm.style.display = 'block';
    username.textContent = 'Demo User';
});

logoutButton.addEventListener('click', () => {
    const cognitoUser = userPool.getCurrentUser();
    if (cognitoUser) {
        cognitoUser.signOut();
    }
    
    loginButton.style.display = 'block';
    userInfo.style.display = 'none';
    sowForm.style.display = 'none';
    resultSection.style.display = 'none';
});

// Add new phase
addPhaseButton.addEventListener('click', () => {
    const phasesContainer = document.getElementById('phases-container');
    const phaseItem = document.createElement('div');
    phaseItem.className = 'phase-item';
    phaseItem.innerHTML = `
        <input type="text" placeholder="Phase name" class="phase-name">
        <textarea placeholder="Phase description" class="phase-desc"></textarea>
        <button class="remove-phase">Remove</button>
    `;
    phasesContainer.appendChild(phaseItem);
    
    // Add event listener to the new remove button
    phaseItem.querySelector('.remove-phase').addEventListener('click', () => {
        phasesContainer.removeChild(phaseItem);
    });
});

// Add new milestone
addMilestoneButton.addEventListener('click', () => {
    const milestonesContainer = document.getElementById('milestones-container');
    const milestoneItem = document.createElement('div');
    milestoneItem.className = 'milestone-item';
    milestoneItem.innerHTML = `
        <input type="text" placeholder="Milestone name" class="milestone-name">
        <input type="date" class="milestone-date">
        <button class="remove-milestone">Remove</button>
    `;
    milestonesContainer.appendChild(milestoneItem);
    
    // Add event listener to the new remove button
    milestoneItem.querySelector('.remove-milestone').addEventListener('click', () => {
        milestonesContainer.removeChild(milestoneItem);
    });
});

// Add new resource
addResourceButton.addEventListener('click', () => {
    const resourcesContainer = document.getElementById('resources-container');
    const resourceItem = document.createElement('div');
    resourceItem.className = 'resource-item';
    resourceItem.innerHTML = `
        <input type="text" placeholder="Resource role" class="resource-role">
        <input type="number" placeholder="Quantity" class="resource-qty" min="1">
        <input type="text" placeholder="Engagement period" class="resource-period">
        <button class="remove-resource">Remove</button>
    `;
    resourcesContainer.appendChild(resourceItem);
    
    // Add event listener to the new remove button
    resourceItem.querySelector('.remove-resource').addEventListener('click', () => {
        resourcesContainer.removeChild(resourceItem);
    });
});

// Generate SOW
generateSowButton.addEventListener('click', async () => {
    // Collect all form data
    const description = document.getElementById('description').value;
    
    const phases = [];
    document.querySelectorAll('.phase-item').forEach(item => {
        phases.push({
            name: item.querySelector('.phase-name').value,
            description: item.querySelector('.phase-desc').value
        });
    });
    
    const milestones = [];
    document.querySelectorAll('.milestone-item').forEach(item => {
        milestones.push({
            name: item.querySelector('.milestone-name').value,
            date: item.querySelector('.milestone-date').value
        });
    });
    
    const resources = [];
    document.querySelectorAll('.resource-item').forEach(item => {
        resources.push({
            role: item.querySelector('.resource-role').value,
            quantity: item.querySelector('.resource-qty').value,
            period: item.querySelector('.resource-period').value
        });
    });
    
    const sowData = {
        description,
        phases,
        milestones,
        resources
    };
    
    try {
        // Call the API to generate the SOW (no auth token needed)
        const response = await fetch(`${awsConfig.apiEndpoint}/generate-sow`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(sowData)
        });
        
        if (!response.ok) {
            throw new Error('Failed to generate SOW');
        }
        
        const data = await response.json();
        
        // Show the result section with download link
        sowForm.style.display = 'none';
        resultSection.style.display = 'block';
        downloadLink.href = data.documentUrl;
    } catch (error) {
        console.error('Error generating SOW:', error);
        alert('Failed to generate SOW. Please try again.');
    }
});

// Create new SOW button
createNewButton.addEventListener('click', () => {
    resultSection.style.display = 'none';
    sowForm.style.display = 'block';
    
    // Reset form fields
    document.getElementById('description').value = '';
    
    // Clear phases except the first one
    const phasesContainer = document.getElementById('phases-container');
    const phaseItems = phasesContainer.querySelectorAll('.phase-item');
    for (let i = 1; i < phaseItems.length; i++) {
        phasesContainer.removeChild(phaseItems[i]);
    }
    phaseItems[0].querySelector('.phase-name').value = '';
    phaseItems[0].querySelector('.phase-desc').value = '';
    
    // Clear milestones except the first one
    const milestonesContainer = document.getElementById('milestones-container');
    const milestoneItems = milestonesContainer.querySelectorAll('.milestone-item');
    for (let i = 1; i < milestoneItems.length; i++) {
        milestonesContainer.removeChild(milestoneItems[i]);
    }
    milestoneItems[0].querySelector('.milestone-name').value = '';
    milestoneItems[0].querySelector('.milestone-date').value = '';
    
    // Clear resources except the first one
    const resourcesContainer = document.getElementById('resources-container');
    const resourceItems = resourcesContainer.querySelectorAll('.resource-item');
    for (let i = 1; i < resourceItems.length; i++) {
        resourcesContainer.removeChild(resourceItems[i]);
    }
    resourceItems[0].querySelector('.resource-role').value = '';
    resourceItems[0].querySelector('.resource-qty').value = '';
    resourceItems[0].querySelector('.resource-period').value = '';
});

// Initialize the app
checkAuth();
