import json
import os
import boto3
import uuid
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from io import BytesIO
import base64
from dateutil import parser

# Initialize AWS clients
s3 = boto3.client('s3')
BUCKET_NAME = os.environ['BUCKET_NAME']
TEMPLATE_KEY = 'templates/sow_template.docx'

def create_timeline_image(milestones):
    """Create a timeline image from milestone data"""
    # Convert milestone dates to datetime objects
    dates = [parser.parse(m['date']) for m in milestones]
    names = [m['name'] for m in milestones]
    
    # Create the figure and plot
    fig, ax = plt.subplots(figsize=(10, 4))
    
    # Plot the timeline
    ax.plot(dates, [1] * len(dates), 'o-', markersize=10)
    
    # Add milestone labels
    for i, (date, name) in enumerate(zip(dates, names)):
        ax.annotate(name, (date, 1), xytext=(0, 10), 
                   textcoords='offset points', ha='center', va='bottom',
                   fontsize=9, rotation=45)
    
    # Format the x-axis
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax.xaxis.set_major_locator(mdates.MonthLocator())
    plt.xticks(rotation=45)
    
    # Remove y-axis ticks and labels
    ax.yaxis.set_visible(False)
    ax.spines['left'].set_visible(False)
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # Set title
    ax.set_title('Project Timeline and Milestones')
    
    # Adjust layout
    plt.tight_layout()
    
    # Save to BytesIO object
    img_data = BytesIO()
    plt.savefig(img_data, format='png')
    img_data.seek(0)
    plt.close()
    
    return img_data

def generate_sow_document(data):
    """Generate a SOW document based on the provided data"""
    # Create a new Document
    doc = Document()
    
    # Add title
    title = doc.add_heading('Statement of Work', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add date
    date_paragraph = doc.add_paragraph()
    date_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    date_paragraph.add_run(f'Date: {datetime.now().strftime("%B %d, %Y")}')
    
    # Add description section
    doc.add_heading('1. Description and Purpose', 1)
    doc.add_paragraph(data['description'])
    
    # Add phases section
    doc.add_heading('2. Project Phases', 1)
    for i, phase in enumerate(data['phases']):
        doc.add_heading(f'2.{i+1}. {phase["name"]}', 2)
        doc.add_paragraph(phase['description'])
    
    # Add timeline section
    doc.add_heading('3. Timeline and Milestones', 1)
    
    # Create and add timeline image if there are milestones
    if data['milestones']:
        timeline_img = create_timeline_image(data['milestones'])
        doc.add_picture(timeline_img, width=Inches(6))
        
        # Add milestone details in a table
        table = doc.add_table(rows=1, cols=2)
        table.style = 'Table Grid'
        
        # Add header row
        header_cells = table.rows[0].cells
        header_cells[0].text = 'Milestone'
        header_cells[1].text = 'Target Date'
        
        # Add milestone rows
        for milestone in data['milestones']:
            row_cells = table.add_row().cells
            row_cells[0].text = milestone['name']
            row_cells[1].text = milestone['date']
    
    # Add resources section
    doc.add_heading('4. Resource Estimates', 1)
    
    if data['resources']:
        table = doc.add_table(rows=1, cols=3)
        table.style = 'Table Grid'
        
        # Add header row
        header_cells = table.rows[0].cells
        header_cells[0].text = 'Role'
        header_cells[1].text = 'Quantity'
        header_cells[2].text = 'Engagement Period'
        
        # Add resource rows
        for resource in data['resources']:
            row_cells = table.add_row().cells
            row_cells[0].text = resource['role']
            row_cells[1].text = str(resource['quantity'])
            row_cells[2].text = resource['period']
    
    # Save the document to a BytesIO object
    doc_io = BytesIO()
    doc.save(doc_io)
    doc_io.seek(0)
    
    return doc_io

def lambda_handler(event, context):
    try:
        # Parse the input data
        body = json.loads(event['body'])
        
        # Generate the SOW document
        doc_io = generate_sow_document(body)
        
        # Generate a unique filename
        filename = f"sow_{uuid.uuid4()}.docx"
        
        # Upload the document to S3
        s3.upload_fileobj(
            doc_io, 
            BUCKET_NAME, 
            f"documents/{filename}",
            ExtraArgs={'ContentType': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'}
        )
        
        # Generate a pre-signed URL for downloading the document
        presigned_url = s3.generate_presigned_url(
            'get_object',
            Params={'Bucket': BUCKET_NAME, 'Key': f"documents/{filename}"},
            ExpiresIn=3600  # URL expires in 1 hour
        )
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps