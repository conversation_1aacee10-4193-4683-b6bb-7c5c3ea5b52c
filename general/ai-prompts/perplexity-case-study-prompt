********** Case Study Perplexity Prompt **********
Assume the role of a sales and marketing professional building a new sales pipeline for prospects. You need to create a series of marketing artifacts/use cases detailing the benefits customers have experienced using services of a managed services provider, this case study is on resiliency and reliability.

Referencing the attached reliability-bcbsmn (rough outline) I need you to draft a case study document using this URL as an example of the finished product: https://aws.amazon.com/solutions/case-studies/tufts-case-study/?did=cr_card&trk=cr_card

The document should be professionally written, must avoid using weasel words or unnecessary embellishment, needs to be drafted in STAR format (Situation, Task, Action, Result) and as much as possible should contain as many benefits that were realized as possible, including any before/after commentary

Also do not include any hypothetical conversations or quotes if they have not been expressly included in the attached document. Double check and validate your response against the actual attached document.

Organize the document as follows:

Summary (executive summary)
Situation (include as many details as needed including the time spent to engage and perform the work, how many resources were brought in and anything else relevant to the story)
Tasks requested from fortellar resources assigned
Actions taken by fortellar as well as the customer
Results experienced by working with fortellar
Future or follow up items discovered during the engagement and how those may lead to additional work.

Lastly make sure the output is written in document format so I can cut/paste into word, or download the word document


********** SOW Agent Creation **********

SOW Creation Agent:

Your role is a senior level software engineer, you have been tasked with creating a simplistic agentic AI product to create statement of work (SOW) documentations based on previously approved versions. The solution should produce a word document with a standard format, during the creation of the SOW the agents should prompt the user for details: 1\ description and purpose 2\ phases that will be documented in the SOW 3\ timeline that highlights milestones in the document (visual graphic from start to finish) 4\ high-level resource estimates (how many resources, when will they engage on the timeline). The solution should use AWS native capabilities focusing on free tier services and recommending any pay-as-you-go services that may be needed. This needs to be a very simplistic architecture with low overhead for maintenance. 

Response: too high level

Your role is a senior level software engineer, you have been tasked with creating a simplistic agentic AI product to create statement of work (SOW) documentations based on previously approved versions. The solution should produce a word document with a standard format, during the creation of the SOW the agents should prompt the user for details: 1\ description and purpose 2\ phases that will be documented in the SOW 3\ timeline that highlights milestones in the document (visual graphic from start to finish) 4\ high-level resource estimates (how many resources, when will they engage on the timeline). The solution should use AWS native capabilities focusing on free tier services and recommending any pay-as-you-go services that may be needed. This needs to be a very simplistic architecture with low overhead for maintenance. I need you to create the actual architecture, code needed, version control repository including all of the git repository artifacts needed to deploy the solution to AWS, cloud formation code to deploy the package (make sure it can all be rolled back as well).

