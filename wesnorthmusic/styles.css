/* Reset and Base Styles - DeVotchKa Inspired */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.7;
    color: #ffffff;
    background-color: #000000;
    font-weight: 300;
    letter-spacing: 0.5px;
}

/* Color Variables - DeVotchKa Dark Theme */
:root {
    --primary-color: #ffffff;
    --secondary-color: #cccccc;
    --accent-color: #888888;
    --text-color: #ffffff;
    --text-light: #cccccc;
    --text-muted: #888888;
    --background-color: #000000;
    --background-secondary: #111111;
    --background-tertiary: #222222;
    --border-color: #333333;
    --hover-color: #333333;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Navigation */
.navbar {
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.nav-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.nav-logo a {
    font-size: 1.8rem;
    font-weight: 300;
    color: var(--primary-color);
    text-decoration: none;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.nav-menu {
    display: flex;
    gap: 3rem;
}

.nav-link {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 300;
    transition: color 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    font-size: 0.85rem;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 2px;
    background: var(--primary-color);
    margin: 4px 0;
    transition: 0.3s;
}

/* Hero Section - DeVotchKa Minimalist Style */
.hero {
    margin-top: 80px;
    padding: 120px 0;
    background: var(--background-color);
    color: var(--primary-color);
    text-align: center;
}

.hero-content {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
}

.hero-text {
    text-align: left;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero h1 {
    font-size: 4rem;
    font-weight: 300;
    margin-bottom: 2rem;
    letter-spacing: 3px;
    text-transform: uppercase;
}

.hero-subtitle {
    font-size: 1.2rem;
    font-weight: 300;
    margin-bottom: 3rem;
    color: var(--text-light);
    letter-spacing: 1px;
}

.hero-description {
    font-size: 1rem;
    margin-bottom: 3rem;
    color: var(--text-light);
    line-height: 1.8;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 2rem;
    justify-content: center;
}

.btn {
    padding: 15px 30px;
    border: 1px solid var(--border-color);
    background: transparent;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 300;
    transition: all 0.3s ease;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

.btn-primary {
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-color);
    color: var(--background-color);
}

.btn-secondary {
    border-color: var(--text-light);
    color: var(--text-light);
}

.btn-secondary:hover {
    background: var(--text-light);
    color: var(--background-color);
}

.artist-photo {
    width: 100%;
    max-width: 400px;
    border-radius: 8px;
    opacity: 0.95;
    margin: 0 auto;
    display: block;
    border: 2px solid var(--border-color);
    background: var(--background-secondary);
    box-shadow: 0 8px 32px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.artist-photo:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 48px rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
}

.artist-photo:not([src]),
.artist-photo[src=""] {
    background: var(--background-secondary);
    border: 2px dashed var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.artist-photo:not([src])::before,
.artist-photo[src=""]::before {
    content: "Artist Photo";
}

/* About Section - Minimalist */
.about {
    padding: 120px 0;
    background: var(--background-secondary);
}

.about h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 4rem;
    color: var(--primary-color);
    font-weight: 300;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-text p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 2;
    color: var(--text-light);
}

.about-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.feature {
    text-align: center;
    padding: 2rem;
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
}

.feature i {
    font-size: 2rem;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.feature h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Latest Release Section */
.latest-release {
    padding: 120px 0;
    background: var(--background-color);
}

.latest-release h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 4rem;
    color: var(--primary-color);
    font-weight: 300;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.release-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: start;
}

.release-image img {
    width: 100%;
    border-radius: 8px;
    border: 2px solid var(--border-color);
    background: var(--background-secondary);
    box-shadow: 0 8px 32px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.release-image img:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 48px rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
}

.release-image img:not([src]),
.release-image img[src=""] {
    background: var(--background-secondary);
    border: 2px dashed var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-height: 300px;
}

.release-image img:not([src])::before,
.release-image img[src=""]::before {
    content: "Album Cover";
}

.release-info h3 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.release-info p {
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.release-date {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    font-style: italic;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.track-list {
    margin: 2rem 0;
}

.track-list h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 1rem;
}

.track-list ol {
    margin-left: 1.5rem;
    color: var(--text-light);
}

.track-list li {
    margin-bottom: 0.5rem;
    font-weight: 300;
}

.release-credits {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-left: 3px solid var(--primary-color);
}

.release-credits p {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.release-credits strong {
    color: var(--primary-color);
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

/* Footer - Minimalist Dark */
.footer {
    background: var(--background-secondary);
    color: var(--text-light);
    padding: 4rem 0 2rem;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section ul li a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
    font-weight: 300;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1.5rem;
}

.social-links a {
    color: var(--text-light);
    font-size: 1.3rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Page Header - Minimalist */
.page-header {
    margin-top: 80px;
    padding: 100px 0;
    background: var(--background-color);
    color: var(--primary-color);
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 300;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.page-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 300;
}

/* Streaming Platforms */
.streaming-platforms {
    padding: 80px 0;
    background: white;
}

.streaming-platforms h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.platforms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.platform-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.platform-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-color: #e74c3c;
}

.platform-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #e74c3c;
}

.platform-card h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.platform-card p {
    color: #666;
    font-size: 0.9rem;
}

/* Featured Release */
.featured-release {
    padding: 80px 0;
    background: #f8f9fa;
}

.featured-release h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.release-showcase {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.release-player iframe {
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.album-player-placeholder {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid var(--border-color);
    background: var(--background-secondary);
}

.album-cover-large {
    width: 100%;
    height: auto;
    display: block;
}

.player-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.album-player-placeholder:hover .player-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--background-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
}

.player-overlay p {
    color: var(--primary-color);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
}

.release-details h3 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.release-date {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    font-style: italic;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.release-description {
    margin-bottom: 2rem;
    line-height: 1.8;
    color: var(--text-light);
}

.track-listing {
    margin-bottom: 2rem;
}

.track-listing h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 1rem;
}

.track-listing ol {
    list-style: none;
    counter-reset: track-counter;
}

.track-listing li {
    counter-increment: track-counter;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-light);
}

.track-listing li::before {
    content: counter(track-counter, decimal-leading-zero);
    font-weight: 400;
    color: var(--primary-color);
    margin-right: 1rem;
    font-size: 0.9rem;
}

.track-listing span {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.release-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-outline {
    background: transparent;
    color: #e74c3c;
    border: 2px solid #e74c3c;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-outline:hover {
    background: #e74c3c;
    color: white;
}

/* Social Media Section */
.social-media {
    padding: 80px 0;
    background: white;
}

.social-media h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.social-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.social-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
}

.social-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.social-icon.youtube {
    background: #ff0000;
}

.social-icon.facebook {
    background: #1877f2;
}

.social-icon.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-card h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.social-card p {
    margin-bottom: 1.5rem;
    color: #666;
}

/* Music Info Section */
.music-info {
    padding: 80px 0;
    background: #f8f9fa;
    text-align: center;
}

.music-info h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #2c3e50;
}

.genre-tags {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
}

.tag {
    background: #e74c3c;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.genre-description {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
}

/* Contact Page Styles */
.contact-section {
    padding: 80px 0;
    background: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.contact-info > p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
}

.contact-methods {
    margin-bottom: 3rem;
}

.contact-method {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: #e74c3c;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.contact-details h3 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.contact-details p {
    color: #666;
}

.social-connect h3 {
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.social-links-large {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.social-link i {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.social-link.youtube:hover {
    background: #ff0000;
    color: white;
}

.social-link.facebook:hover {
    background: #1877f2;
    color: white;
}

.social-link.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: white;
}

.social-link.bandcamp:hover {
    background: #629aa0;
    color: white;
}

/* Contact Form */
.contact-form-container h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #2c3e50;
}

.contact-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #e74c3c;
}

.captcha-group {
    background: #fff;
    padding: 1rem;
    border-radius: 6px;
    border: 2px solid #ddd;
}

.captcha-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#captcha-question {
    font-weight: 500;
    color: #2c3e50;
}

#captcha {
    max-width: 100px;
}

.form-message {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.form-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.form-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.btn-submit {
    width: 100%;
    padding: 15px;
    font-size: 1.1rem;
}

.form-note {
    margin-top: 2rem;
    padding: 1rem;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    color: #856404;
    font-size: 0.9rem;
}

/* Shows Page Styles - Minimalist */
.shows-management {
    padding: 120px 0;
    background: var(--background-secondary);
}

.shows-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
}

.shows-header h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    font-weight: 300;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.shows-calendar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.show-item {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 2rem;
    align-items: center;
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
    padding: 2rem;
    transition: all 0.3s ease;
}

.show-item:hover {
    border-color: var(--primary-color);
}

.show-date {
    text-align: center;
    min-width: 80px;
}

.date-day {
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--primary-color);
    line-height: 1;
}

.date-month {
    font-size: 1rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.5rem;
}

.date-year {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.show-details h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 300;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.show-location {
    color: var(--text-light);
    margin-bottom: 0.3rem;
}

.show-time {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.show-description {
    color: var(--text-muted);
    font-style: italic;
}

.show-actions {
    text-align: right;
}

.show-status {
    color: var(--text-muted);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: 8px 16px;
    text-decoration: none;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--background-color);
}

.shows-note {
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
    text-align: center;
}

/* Showcases Section */
.showcases-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.showcases-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.showcases-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
}

.showcases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.showcase-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.showcase-card:hover {
    transform: translateY(-5px);
}

.showcase-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.showcase-header h3 {
    color: #2c3e50;
    font-size: 1.3rem;
}

.showcase-frequency {
    background: #e74c3c;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.showcase-details p {
    margin-bottom: 0.8rem;
    color: #555;
}

.showcase-details strong {
    color: #2c3e50;
}

.showcase-actions {
    margin-top: 1.5rem;
}

/* Booking Info */
.booking-info {
    padding: 80px 0;
    background: white;
}

.booking-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.booking-text h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.booking-text ul {
    margin: 1.5rem 0;
    padding-left: 2rem;
}

.booking-text li {
    margin-bottom: 0.5rem;
    color: #555;
}

.booking-contact {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
}

.booking-contact h3 {
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.contact-info-compact p {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: #555;
}

.contact-info-compact i {
    margin-right: 0.5rem;
    color: #e74c3c;
    width: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-text {
        text-align: center;
    }

    .artist-photo {
        max-width: 280px;
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .release-content {
        grid-template-columns: 1fr;
    }

    .release-showcase {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .platforms-grid {
        grid-template-columns: 1fr;
    }

    .social-grid {
        grid-template-columns: 1fr;
    }

    .release-actions {
        flex-direction: column;
    }

    .genre-tags {
        justify-content: center;
    }

    /* Contact page responsive */
    .contact-content {
        grid-template-columns: 1fr;
    }

    .social-links-large {
        grid-template-columns: 1fr;
    }

    /* Shows page responsive */
    .shows-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .show-item {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1rem;
    }

    .show-actions {
        text-align: center;
    }

    .showcases-grid {
        grid-template-columns: 1fr;
    }

    .booking-content {
        grid-template-columns: 1fr;
    }

    .showcase-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}
