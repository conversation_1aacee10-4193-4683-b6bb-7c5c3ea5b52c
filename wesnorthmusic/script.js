// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!navToggle.contains(event.target) && !navMenu.contains(event.target)) {
                navMenu.classList.remove('active');
            }
        });
    }

    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 70; // Account for fixed navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Active navigation highlighting
    function updateActiveNav() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPage) {
                link.classList.add('active');
            }
        });
    }

    updateActiveNav();

    // Load shows from localStorage for shows page
    if (window.location.pathname.includes('shows.html')) {
        loadStaticShows();
    }

    // Handle image loading errors
    setupImageFallbacks();

    // Navbar background on scroll (updated for dark theme)
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(0, 0, 0, 0.95)';
            navbar.style.backdropFilter = 'blur(10px)';
        } else {
            navbar.style.background = 'var(--background-color)';
            navbar.style.backdropFilter = 'none';
        }
    });

    // Form validation and submission (for contact form)
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Basic form validation
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const message = document.getElementById('message').value.trim();
            const captcha = document.getElementById('captcha').value.trim();
            const captchaAnswer = document.getElementById('captcha-answer').value;

            if (!name || !email || !message) {
                showMessage('Please fill in all required fields.', 'error');
                return;
            }

            if (!isValidEmail(email)) {
                showMessage('Please enter a valid email address.', 'error');
                return;
            }

            if (captcha !== captchaAnswer) {
                showMessage('Please solve the CAPTCHA correctly.', 'error');
                generateCaptcha(); // Generate new CAPTCHA
                return;
            }

            // Simulate form submission
            showMessage('Thank you for your message! We\'ll get back to you soon.', 'success');
            contactForm.reset();
            generateCaptcha(); // Generate new CAPTCHA
        });
    }

    // Generate CAPTCHA
    function generateCaptcha() {
        const captchaElement = document.getElementById('captcha-question');
        const captchaAnswerElement = document.getElementById('captcha-answer');
        
        if (captchaElement && captchaAnswerElement) {
            const num1 = Math.floor(Math.random() * 10) + 1;
            const num2 = Math.floor(Math.random() * 10) + 1;
            const answer = num1 + num2;
            
            captchaElement.textContent = `What is ${num1} + ${num2}?`;
            captchaAnswerElement.value = answer;
        }
    }

    // Initialize CAPTCHA on page load
    generateCaptcha();

    // Email validation
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Show message function
    function showMessage(message, type) {
        const messageDiv = document.getElementById('form-message');
        if (messageDiv) {
            messageDiv.textContent = message;
            messageDiv.className = `form-message ${type}`;
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }
    }

});

// Static show calendar functionality
function loadStaticShows() {
    const savedShows = localStorage.getItem('wesNorthShows');
    if (savedShows) {
        const shows = JSON.parse(savedShows);
        const showsCalendar = document.getElementById('shows-calendar');

        if (showsCalendar && shows.length > 0) {
            // Clear existing shows (keep the placeholder comments)
            const startComment = '<!-- SHOWS_PLACEHOLDER_START -->';
            const endComment = '<!-- SHOWS_PLACEHOLDER_END -->';
            const startIndex = showsCalendar.innerHTML.indexOf(startComment);
            const endIndex = showsCalendar.innerHTML.indexOf(endComment) + endComment.length;

            if (startIndex !== -1 && endIndex !== -1) {
                const beforePlaceholder = showsCalendar.innerHTML.substring(0, startIndex);
                const afterPlaceholder = showsCalendar.innerHTML.substring(endIndex);

                // Generate new show items
                const showItems = shows.map(show => {
                    const date = new Date(show.date);
                    const day = date.getDate().toString().padStart(2, '0');
                    const month = date.toLocaleDateString('en-US', { month: 'short' });
                    const year = date.getFullYear();

                    const time = formatTime(show.time);

                    return `
                <div class="show-item">
                    <div class="show-date">
                        <div class="date-day">${day}</div>
                        <div class="date-month">${month}</div>
                        <div class="date-year">${year}</div>
                    </div>
                    <div class="show-details">
                        <h3>${show.venue}</h3>
                        <p class="show-location">${show.city}</p>
                        <p class="show-time">${time}</p>
                        ${show.description ? `<p class="show-description">${show.description}</p>` : ''}
                    </div>
                    <div class="show-actions">
                        ${show.link ? `<a href="${show.link}" target="_blank" class="btn btn-outline">Tickets</a>` : '<span class="show-status">Upcoming</span>'}
                    </div>
                </div>`;
                }).join('');

                showsCalendar.innerHTML = beforePlaceholder + startComment + showItems + endComment + afterPlaceholder;
            }
        }
    }
}

function formatTime(timeStr) {
    const [hours, minutes] = timeStr.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// Image fallback system
function setupImageFallbacks() {
    const images = document.querySelectorAll('img');

    images.forEach(img => {
        // Handle images that fail to load
        img.addEventListener('error', function() {
            this.style.display = 'none';

            // Create a fallback div
            const fallback = document.createElement('div');
            fallback.className = 'image-fallback';
            fallback.style.cssText = `
                width: ${this.offsetWidth || 300}px;
                height: ${this.offsetHeight || 300}px;
                background: var(--background-secondary);
                border: 2px dashed var(--border-color);
                display: flex;
                align-items: center;
                justify-content: center;
                color: var(--text-muted);
                font-size: 0.9rem;
                text-transform: uppercase;
                letter-spacing: 1px;
                border-radius: 2px;
            `;

            // Set fallback text based on alt attribute
            const altText = this.alt || 'Image';
            fallback.textContent = altText.includes('Photo') ? 'Artist Photo' :
                                  altText.includes('Cover') ? 'Album Cover' :
                                  'Image Placeholder';

            // Insert fallback after the image
            this.parentNode.insertBefore(fallback, this.nextSibling);
        });

        // Handle images that are already broken
        if (img.complete && img.naturalHeight === 0) {
            img.dispatchEvent(new Event('error'));
        }
    });
}
