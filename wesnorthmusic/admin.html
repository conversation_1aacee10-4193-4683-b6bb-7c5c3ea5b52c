<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Manage Shows | Wes North Music</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px;
            background: var(--background-secondary);
            min-height: 100vh;
        }
        
        .admin-header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .admin-header h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            font-weight: 300;
            letter-spacing: 2px;
            text-transform: uppercase;
            margin-bottom: 1rem;
        }
        
        .admin-section {
            background: var(--background-tertiary);
            border: 1px solid var(--border-color);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .admin-section h2 {
            color: var(--primary-color);
            font-size: 1.5rem;
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1.5rem;
        }
        
        .show-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 1rem;
            background: var(--background-color);
            border: 1px solid var(--border-color);
        }
        
        .show-form input, .show-form textarea {
            padding: 10px;
            background: var(--background-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            font-family: inherit;
        }
        
        .show-form input:focus, .show-form textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .show-list {
            margin-top: 2rem;
        }
        
        .show-item {
            background: var(--background-color);
            border: 1px solid var(--border-color);
            padding: 1rem;
            margin-bottom: 1rem;
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1rem;
            align-items: center;
        }
        
        .show-details {
            color: var(--text-light);
        }
        
        .show-date {
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .output-section {
            background: var(--background-color);
            border: 1px solid var(--border-color);
            padding: 1rem;
            margin-top: 2rem;
        }
        
        .output-section h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-weight: 300;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .output-code {
            background: var(--background-secondary);
            border: 1px solid var(--border-color);
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: var(--text-light);
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .btn-admin {
            background: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
        }
        
        .btn-admin:hover {
            background: var(--primary-color);
            color: var(--background-color);
        }
        
        .btn-danger {
            border-color: #ff6b6b;
            color: #ff6b6b;
        }
        
        .btn-danger:hover {
            background: #ff6b6b;
            color: var(--background-color);
        }
        
        .instructions {
            background: var(--background-tertiary);
            border-left: 3px solid var(--primary-color);
            padding: 1rem;
            margin-bottom: 2rem;
            color: var(--text-light);
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .notification {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            font-weight: 500;
            display: none;
        }

        .notification.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .notification.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .notification.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Show Management Admin</h1>
            <p style="color: var(--text-light);">Manage your upcoming shows and generate static calendar data</p>
        </div>

        <div id="notification" class="notification"></div>
        
        <div class="instructions">
            <strong>Semi-Automated Show Management:</strong> This admin system automatically generates an updated shows.html file when you add, edit, or delete events.
            <br><br>
            <strong>How It Works:</strong>
            <ul style="margin: 1rem 0; padding-left: 2rem; color: var(--text-light);">
                <li>Add or remove shows using the form below</li>
                <li>The system automatically downloads an updated shows.html file</li>
                <li>Replace your current shows.html file with the downloaded version</li>
                <li>Your website is immediately updated with the latest events</li>
                <li>Much faster than manual copying and pasting!</li>
            </ul>
        </div>
        
        <div class="admin-section">
            <h2>Add New Show</h2>
            <div class="show-form" id="show-form">
                <input type="date" id="show-date" placeholder="Date" required>
                <input type="time" id="show-time" placeholder="Time" required>
                <input type="text" id="show-venue" placeholder="Venue Name" required>
                <input type="text" id="show-city" placeholder="City, State" required>
                <input type="text" id="show-description" placeholder="Event Description">
                <input type="url" id="show-link" placeholder="Ticket/Info Link (optional)">
                <button type="button" class="btn-admin" onclick="addShow()">Add Show</button>
            </div>
        </div>
        
        <div class="admin-section">
            <h2>Current Shows</h2>
            <div id="show-list" class="show-list">
                <!-- Shows will be populated here -->
            </div>
        </div>
        
        <div class="admin-section">
            <h2>Generated HTML Code</h2>
            <p style="color: var(--text-light); margin-bottom: 1rem;">Copy this code and paste it into your shows.html file to update the calendar:</p>
            <div class="output-section">
                <h3>HTML for shows.html</h3>
                <div class="output-code" id="html-output"></div>
                <button type="button" class="btn-admin" onclick="copyToClipboard('html-output')">Copy HTML</button>
            </div>
        </div>
        
        <div class="admin-section">
            <h2>CSV Data Management</h2>
            <div class="output-section">
                <h3>CSV Data</h3>
                <div class="output-code" id="csv-output"></div>
                <button type="button" class="btn-admin" onclick="downloadCSV()">Download CSV File</button>
                <button type="button" class="btn-admin" onclick="copyToClipboard('csv-output')">Copy CSV</button>
            </div>
        </div>

        <div class="admin-section">
            <h2>Actions</h2>
            <button type="button" class="btn-admin" onclick="exportData()">Export JSON Backup</button>
            <button type="button" class="btn-admin" onclick="importData()">Import JSON</button>
            <button type="button" class="btn-admin" onclick="importCSV()">Import CSV</button>
            <button type="button" class="btn-admin btn-danger" onclick="clearAllShows()">Clear All Shows</button>
            <input type="file" id="import-file" accept=".json" style="display: none;" onchange="handleImport(event)">
            <input type="file" id="import-csv-file" accept=".csv" style="display: none;" onchange="handleCSVImport(event)">
        </div>
        
        <div style="text-align: center; margin-top: 3rem; padding-top: 2rem; border-top: 1px solid var(--border-color);">
            <a href="index.html" class="btn-admin">← Back to Website</a>
        </div>
    </div>

    <script>
        // Show management system - Clear existing shows to fix date issues
        localStorage.removeItem('wesNorthShows');
        let shows = [];

        // Initialize with empty state and generate the "come back soon" message
        document.addEventListener('DOMContentLoaded', function() {
            renderShows();
            generateHTML();
            generateCSV();
            updateShowsHtmlFile(); // Generate the initial shows.html with "come back soon" message
        });

        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        async function updateShowsHtmlFile() {
            try {
                // Generate the HTML content for shows
                const htmlContent = generateShowsHTML();

                // Read the current shows.html file
                const response = await fetch('shows.html');
                const currentContent = await response.text();

                // Find the placeholder comments and replace content between them
                const startMarker = '<!-- SHOWS_PLACEHOLDER_START -->';
                const endMarker = '<!-- SHOWS_PLACEHOLDER_END -->';

                const startIndex = currentContent.indexOf(startMarker);
                const endIndex = currentContent.indexOf(endMarker);

                if (startIndex === -1 || endIndex === -1) {
                    throw new Error('Could not find placeholder markers in shows.html');
                }

                // Build the new content
                const beforePlaceholder = currentContent.substring(0, startIndex + startMarker.length);
                const afterPlaceholder = currentContent.substring(endIndex);
                const newContent = beforePlaceholder + '\n' + htmlContent + '\n                ' + afterPlaceholder;

                // Save the updated content
                await saveFileContent('shows.html', newContent);

                showNotification('✅ Updated shows.html file generated and downloaded! Replace your current shows.html file to update your website.', 'success');

            } catch (error) {
                console.error('Error updating shows.html:', error);
                showNotification('❌ Could not automatically update shows.html. Please copy the HTML code manually.', 'error');
            }
        }

        async function saveFileContent(filename, content) {
            // Create a downloadable file with the updated content
            const blob = new Blob([content], { type: 'text/html;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            // Note: Due to browser security restrictions, we can't directly overwrite files
            // This downloads the updated file that the user can replace manually
            showNotification('📁 Updated shows.html file downloaded! Please replace your current shows.html file with the downloaded version.', 'info');
        }
        
        function addShow() {
            const date = document.getElementById('show-date').value;
            const time = document.getElementById('show-time').value;
            const venue = document.getElementById('show-venue').value;
            const city = document.getElementById('show-city').value;
            const description = document.getElementById('show-description').value;
            const link = document.getElementById('show-link').value;
            
            if (!date || !time || !venue || !city) {
                showNotification('Please fill in all required fields (Date, Time, Venue, City)', 'error');
                return;
            }

            // Debug: Log the date handling
            console.log('Original date input:', date);
            console.log('Formatted date will be:', formatDate(date));

            // Visual debug - show in the interface
            const debugInfo = `
                DEBUG INFO:
                - Input date: ${date}
                - Formatted: ${formatDate(date)}
                - Day extracted: ${date.split('-')[2]}
                - Month extracted: ${date.split('-')[1]}
            `;
            showNotification(debugInfo, 'info');

            const show = {
                id: Date.now(),
                date,
                time,
                venue,
                city,
                description,
                link
            };
            
            shows.push(show);
            shows.sort((a, b) => new Date(a.date) - new Date(b.date));
            saveShows();
            renderShows();
            clearForm();

            // Automatically update the shows.html file
            updateShowsHtmlFile();

            // Debug: Show what date is actually stored and displayed
            const storedDate = show.date;
            const displayedDate = formatDate(storedDate);

            showNotification(`✅ Show added successfully: "${venue}" on ${displayedDate}. Updated shows.html file downloaded!`, 'success');

            // Additional debug notification
            setTimeout(() => {
                showNotification(`DEBUG: Stored date="${storedDate}", Displayed="${displayedDate}"`, 'info');
            }, 2000);
        }
        
        function removeShow(id) {
            const showToRemove = shows.find(show => show.id === id);
            if (confirm('Are you sure you want to remove this show?')) {
                shows = shows.filter(show => show.id !== id);
                saveShows();
                renderShows();

                // Automatically update the shows.html file
                updateShowsHtmlFile();

                showNotification(`🗑️ Show deleted: "${showToRemove.venue}" on ${formatDate(showToRemove.date)}. Updated shows.html file downloaded!`, 'info');
            }
        }
        
        function saveShows() {
            localStorage.setItem('wesNorthShows', JSON.stringify(shows));
            generateHTML();
            generateCSV();
        }
        
        function renderShows() {
            const container = document.getElementById('show-list');
            if (shows.length === 0) {
                container.innerHTML = '<p style="color: var(--text-muted);">No shows added yet.</p>';
                return;
            }
            
            container.innerHTML = shows.map(show => `
                <div class="show-item">
                    <div class="show-details">
                        <div class="show-date">${formatDate(show.date)} at ${formatTime(show.time)}</div>
                        <div><strong>${show.venue}</strong> - ${show.city}</div>
                        ${show.description ? `<div>${show.description}</div>` : ''}
                        ${show.link ? `<div><a href="${show.link}" target="_blank" style="color: var(--primary-color);">Tickets/Info</a></div>` : ''}
                    </div>
                    <button class="btn-admin btn-danger" onclick="removeShow(${show.id})">Remove</button>
                </div>
            `).join('');
        }

        function generateShowsHTML() {
            if (shows.length === 0) {
                return `                <div class="show-item" style="text-align: center; padding: 3rem 2rem; background: var(--background-tertiary); border-radius: 8px; margin: 2rem 0;">
                    <h3 style="color: var(--text-primary); margin-bottom: 1rem;">No Shows Currently Scheduled</h3>
                    <p style="color: var(--text-light); font-size: 1.1rem;">Come back soon for upcoming performance dates!</p>
                    <p style="color: var(--text-muted); font-size: 0.9rem; margin-top: 1rem;">Follow Wes North on social media for the latest updates.</p>
                </div>`;
            }

            let html = '';
            shows.forEach(show => {
                // Parse date components directly to avoid any timezone issues
                const [year, month, day] = show.date.split('-');
                const dateObj = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
                const monthName = dateObj.toLocaleDateString('en-US', { month: 'short' });

                html += `                <div class="show-item">
                    <div class="show-date">
                        <span class="month">${monthName}</span>
                        <span class="day">${parseInt(day)}</span>
                    </div>
                    <div class="show-details">
                        <h3>${show.venue}</h3>
                        <p class="show-location">${show.city}</p>
                        <p class="show-time">${formatTime(show.time)}</p>
                        ${show.description ? `<p class="show-description">${show.description}</p>` : ''}
                    </div>
                    ${show.link ? `<div class="show-actions">
                        <a href="${show.link}" class="btn btn-outline" target="_blank">Get Tickets</a>
                    </div>` : ''}
                </div>
`;
            });

            return html;
        }

        function generateHTML() {
            const htmlOutput = document.getElementById('html-output');

            if (shows.length === 0) {
                htmlOutput.textContent = '<p style="color: var(--text-muted); text-align: center; padding: 2rem;">No upcoming shows scheduled.</p>';
                return;
            }

            const html = shows.map(show => `
                        <tr>
                            <td><input type="date" class="show-date" value="${show.date}" readonly></td>
                            <td><input type="time" class="show-time" value="${show.time}" readonly></td>
                            <td><input type="text" class="show-venue" value="${show.venue}" readonly></td>
                            <td><input type="text" class="show-city" value="${show.city}" readonly></td>
                            <td><input type="text" class="show-description" value="${show.description || ''}" readonly></td>
                            <td><input type="url" class="show-link" value="${show.link || ''}" readonly></td>
                            <td>
                                <span style="color: var(--text-muted); font-size: 0.8rem;">Static</span>
                            </td>
                        </tr>`).join('');

            htmlOutput.textContent = html;
        }

        function generateCSV() {
            const csvOutput = document.getElementById('csv-output');

            if (shows.length === 0) {
                csvOutput.textContent = 'Date,Time,Venue,City,Description,Link\n# No shows currently scheduled';
                return;
            }

            // CSV Header
            const header = 'Date,Time,Venue,City,Description,Link';

            // CSV Rows
            const rows = shows.map(show => {
                const escapeCsvField = (field) => {
                    if (!field) return '';
                    // Escape quotes and wrap in quotes if contains comma, quote, or newline
                    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
                        return '"' + field.replace(/"/g, '""') + '"';
                    }
                    return field;
                };

                return [
                    show.date,
                    show.time,
                    escapeCsvField(show.venue),
                    escapeCsvField(show.city),
                    escapeCsvField(show.description || ''),
                    escapeCsvField(show.link || '')
                ].join(',');
            });

            const csvContent = header + '\n' + rows.join('\n');
            csvOutput.textContent = csvContent;
        }

        function downloadCSV() {
            const csvContent = document.getElementById('csv-output').textContent;
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'wes-north-shows.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function clearForm() {
            document.getElementById('show-form').reset();
        }
        
        function formatDate(dateStr) {
            // Parse date as local date to avoid timezone issues
            const [year, month, day] = dateStr.split('-').map(num => parseInt(num));
            const date = new Date(year, month - 1, day);

            // Debug logging
            console.log(`formatDate input: ${dateStr}`);
            console.log(`Parsed: year=${year}, month=${month}, day=${day}`);
            console.log(`Created date object: ${date}`);
            console.log(`Formatted result: ${date.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}`);

            return date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
        
        function formatTime(timeStr) {
            const [hours, minutes] = timeStr.split(':');
            const date = new Date();
            date.setHours(parseInt(hours), parseInt(minutes));
            return date.toLocaleTimeString('en-US', { 
                hour: 'numeric', 
                minute: '2-digit',
                hour12: true 
            });
        }
        
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            const type = elementId.includes('html') ? 'HTML' : 'CSV';

            navigator.clipboard.writeText(text).then(() => {
                showNotification(`📋 ${type} code copied to clipboard! Now paste it into your shows.html file to update your website.`, 'success');
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification(`📋 ${type} code copied to clipboard! Now paste it into your shows.html file to update your website.`, 'success');
            });
        }
        
        function exportData() {
            const dataStr = JSON.stringify(shows, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'wes-north-shows.json';
            link.click();
            URL.revokeObjectURL(url);
        }
        
        function importData() {
            document.getElementById('import-file').click();
        }

        function importCSV() {
            document.getElementById('import-csv-file').click();
        }
        
        function handleImport(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedShows = JSON.parse(e.target.result);
                    if (confirm('This will replace all current shows. Continue?')) {
                        shows = importedShows;
                        saveShows();
                        renderShows();
                        alert('Shows imported successfully!');
                    }
                } catch (error) {
                    alert('Error importing file. Please check the file format.');
                }
            };
            reader.readAsText(file);
        }

        function handleCSVImport(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const csvContent = e.target.result;
                    const lines = csvContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));

                    if (lines.length < 2) {
                        alert('CSV file must contain at least a header row and one data row.');
                        return;
                    }

                    const header = lines[0].split(',');
                    const expectedHeaders = ['Date', 'Time', 'Venue', 'City', 'Description', 'Link'];

                    // Basic header validation
                    if (header.length < 4) {
                        alert('CSV must contain at least Date, Time, Venue, and City columns.');
                        return;
                    }

                    const importedShows = [];

                    for (let i = 1; i < lines.length; i++) {
                        const row = parseCSVRow(lines[i]);
                        if (row.length >= 4 && row[0] && row[1] && row[2] && row[3]) {
                            importedShows.push({
                                id: Date.now() + i,
                                date: row[0],
                                time: row[1],
                                venue: row[2],
                                city: row[3],
                                description: row[4] || '',
                                link: row[5] || ''
                            });
                        }
                    }

                    if (importedShows.length === 0) {
                        alert('No valid show data found in CSV file.');
                        return;
                    }

                    if (confirm(`Found ${importedShows.length} shows. This will replace all current shows. Continue?`)) {
                        shows = importedShows;
                        shows.sort((a, b) => new Date(a.date) - new Date(b.date));
                        saveShows();
                        renderShows();
                        alert('Shows imported successfully from CSV!');
                    }
                } catch (error) {
                    alert('Error importing CSV file. Please check the file format.');
                }
            };
            reader.readAsText(file);
        }

        function parseCSVRow(row) {
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < row.length; i++) {
                const char = row[i];

                if (char === '"') {
                    if (inQuotes && row[i + 1] === '"') {
                        current += '"';
                        i++; // Skip next quote
                    } else {
                        inQuotes = !inQuotes;
                    }
                } else if (char === ',' && !inQuotes) {
                    result.push(current);
                    current = '';
                } else {
                    current += char;
                }
            }

            result.push(current);
            return result;
        }
        
        function clearAllShows() {
            if (confirm('Are you sure you want to remove ALL shows? This cannot be undone.')) {
                shows = [];
                saveShows();
                renderShows();
            }
        }
        
        // Initialize
        renderShows();
        generateHTML();
        generateCSV();
    </script>
</body>
</html>
