# Image Setup Guide for Wes North Music Website

## Current Image Status

The website currently uses placeholder images from <PERSON><PERSON>um Photos (https://picsum.photos) which provides random stock photos. These are temporary and should be replaced with actual photos of <PERSON> and album artwork.

## Images Needed

### 1. Artist Photo (Hero Section)
- **Location**: `index.html` line 48
- **Current**: `https://picsum.photos/400/400?random=1`
- **Recommended size**: 400x400 pixels (square)
- **Format**: JPG or PNG
- **Description**: Professional photo of <PERSON> for the main hero section

### 2. Album Cover - "Thus Far" EP
- **Location**: `index.html` line 92
- **Current**: `https://picsum.photos/300/300?random=2`
- **Recommended size**: 300x300 pixels (square)
- **Format**: JPG or PNG
- **Description**: Album artwork for the "Thus Far" EP

## How to Add Your Own Images

### Option 1: Local Images (Recommended)
1. Create an `images` folder in your website directory
2. Add your photos to this folder with descriptive names:
   - `wes-north-photo.jpg` (artist photo)
   - `thus-far-ep-cover.jpg` (album cover)
3. Update the HTML files to reference your local images:

**For the artist photo in index.html:**
```html
<img src="images/wes-north-photo.jpg" alt="Wes North" class="artist-photo">
```

**For the album cover in index.html:**
```html
<img src="images/thus-far-ep-cover.jpg" alt="Thus Far EP Cover">
```

### Option 2: Online Images
If you have images hosted online (like on a cloud service), you can use direct URLs:

```html
<img src="https://your-image-host.com/wes-north-photo.jpg" alt="Wes North" class="artist-photo">
```

## Image Requirements

### Technical Specifications
- **Format**: JPG (for photos) or PNG (for graphics with transparency)
- **Quality**: High resolution but optimized for web (under 500KB per image)
- **Aspect Ratio**: Square (1:1) works best for the current layout

### Content Guidelines
- **Artist Photo**: Professional, high-quality photo that represents your brand
- **Album Cover**: Official artwork for the "Thus Far" EP
- **Style**: Should work well with the dark theme (avoid very dark images that might not show up well)

## Fallback System

The website includes a fallback system that will show placeholder text if images fail to load:
- Artist photos show "Artist Photo" placeholder
- Album covers show "Album Cover" placeholder
- The placeholders match the dark theme styling

## Testing Your Images

After adding your images:
1. Open the website in a browser
2. Check that images load properly
3. Test on mobile devices to ensure they look good at different sizes
4. Verify images work well with the dark theme

## Future Images

As you add more content to your website, follow the same pattern:
1. Add images to the `images` folder
2. Use descriptive filenames
3. Update HTML to reference the new images
4. Test across different devices

## Need Help?

If you need assistance with image optimization or have questions about adding images, the fallback system will ensure your website still looks professional while you work on getting the perfect photos.
